package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.SalesDailyReport;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * {{CHENGQI: 销售日报Mapper接口}}
 * {{CHENGQI: 任务ID: P1-LD-003}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报数据访问层接口，遵循DRY原则复用现有模式}}
 */
@Mapper
public interface SalesDailyReportMapper {
    
    // ==================== 基础CRUD操作 ====================
    
    /**
     * 插入销售日报
     * @param report 销售日报对象
     * @return 影响行数
     */
    int insert(SalesDailyReport report);
    
    /**
     * 根据ID查询销售日报
     * @param id 日报ID
     * @return 销售日报对象
     */
    SalesDailyReport selectById(@Param("id") Long id);

    /**
     * 根据ID查询销售日报（包含员工信息）
     * @param id 日报ID
     * @return 销售日报对象（包含员工姓名、部门名称、评价人姓名）
     */
    SalesDailyReport selectByIdWithEmployeeInfo(@Param("id") Long id);
    
    /**
     * 根据员工ID和日期查询销售日报
     * @param employeeId 员工ID
     * @param reportDate 日报日期
     * @return 销售日报对象
     */
    SalesDailyReport selectByEmployeeAndDate(@Param("employeeId") Integer employeeId, 
                                           @Param("reportDate") LocalDate reportDate);
    
    /**
     * 更新销售日报
     * @param report 销售日报对象
     * @return 影响行数
     */
    int update(SalesDailyReport report);
    
    /**
     * 根据ID删除销售日报
     * @param id 日报ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    // ==================== 分页查询 ====================
    
    /**
     * 分页查询销售日报列表（带员工和部门信息）
     * @param params 查询参数
     * @return 销售日报列表
     */
    List<SalesDailyReport> selectPageWithEmployeeInfo(Map<String, Object> params);
    
    /**
     * 查询销售日报总数
     * @param params 查询参数
     * @return 总数
     */
    int selectCount(Map<String, Object> params);
    
    // ==================== 权限相关查询 ====================
    
    /**
     * 根据员工ID查询日报列表
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日报列表
     */
    List<SalesDailyReport> selectByEmployeeId(@Param("employeeId") Integer employeeId,
                                            @Param("startDate") LocalDate startDate,
                                            @Param("endDate") LocalDate endDate);
    
    /**
     * 根据部门ID查询日报列表（部门负责人权限）
     * @param departmentId 部门ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日报列表
     */
    List<SalesDailyReport> selectByDepartmentId(@Param("departmentId") Integer departmentId,
                                              @Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate);
    
    // ==================== 统计查询（已删除冗余方法） ====================
    // 注：删除了未使用的统计方法：selectReportStatistics, selectResponsibilityDistribution, selectChecklistCompletionStats

    // ==================== JSON字段查询 ====================

    /**
     * 查询包含特定客户的日报
     * @param clientId 客户ID
     * @param clientType 客户类型（inquiry/shipping/key_development）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日报列表
     */
    List<SalesDailyReport> selectByClientId(@Param("clientId") Integer clientId,
                                          @Param("clientType") String clientType,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate);
    
    // ==================== 业务查询 ====================
    
    /**
     * 查询员工最近的日报
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return 最近的日报列表
     */
    List<SalesDailyReport> selectRecentByEmployeeId(@Param("employeeId") Integer employeeId,
                                                   @Param("limit") Integer limit);
    
    /**
     * 检查员工是否已提交指定日期的日报
     * @param employeeId 员工ID
     * @param reportDate 日报日期
     * @return 是否存在
     */
    boolean existsByEmployeeAndDate(@Param("employeeId") Integer employeeId,
                                   @Param("reportDate") LocalDate reportDate);
    
    /**
     * 批量更新统计字段
     * @param reports 日报列表
     * @return 影响行数
     */
    int batchUpdateStatistics(@Param("reports") List<SalesDailyReport> reports);

    /**
     * 更新日报评价信息
     * @param reportId 日报ID
     * @param managerEvaluation 领导评价
     * @param evaluatorId 评价人ID
     * @return 影响行数
     */
    int updateEvaluation(@Param("reportId") Long reportId,
                        @Param("managerEvaluation") String managerEvaluation,
                        @Param("evaluatorId") Integer evaluatorId);
}

// {{CHENGQI: 任务P1-LD-003接口部分完成时间: 2025-06-04 10:52:42 +08:00}}
// {{CHENGQI: 验收状态: Mapper接口定义完成，包含基础CRUD、分页查询、权限控制、统计分析等方法}}
// {{CHENGQI: 设计原则应用: 接口隔离原则(ISP)，方法职责单一，参数明确}}
// {{CHENGQI: 下一步: 创建对应的XML映射文件}}
