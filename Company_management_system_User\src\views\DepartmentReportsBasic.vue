<template>
    <div class="department-content-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
                <div class="search-section">
                    <div class="search-row">
                        <el-cascader
                            v-model="selectedDepartments"
                            :options="departmentOptions"
                            :props="cascaderProps"
                            placeholder="请选择您负责的部门"
                            clearable
                            :loading="loadingDepartments"
                            style="width: 280px; margin-right: 10px;"
                            collapse-tags
                            collapse-tags-tooltip
                            :max-collapse-tags="2"
                            @change="handleDepartmentChange"
                        />
                        <el-input
                            v-model="searchForm.employeeName"
                            placeholder="搜索员工姓名"
                            clearable
                            style="width: 160px"
                            @keyup.enter="handleSearch"
                            @clear="handleSearch"
                        >
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-date-picker
                            v-model="searchForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 220px"
                            @change="handleSearch"
                        />
                        <el-select
                            v-model="searchForm.responsibilityLevel"
                            placeholder="责任心评级"
                            clearable
                            style="width: 120px"
                            @change="handleSearch"
                        >
                            <el-option label="优秀" value="优秀" />
                            <el-option label="中等" value="中等" />
                            <el-option label="差" value="差" />
                        </el-select>
                        <el-button type="primary" @click="handleSearch">
                            <el-icon><Search /></el-icon>搜索
                        </el-button>
                        <el-button @click="handleReset">
                            <el-icon><RefreshRight /></el-icon>重置
                        </el-button>
                    </div>
                </div>
            </div>

            <el-table
                v-loading="loading"
                :data="reportList"
                border
                row-key="id"
                :max-height="'calc(100vh - 220px)'"
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
            >
                <el-table-column type="index" width="60" align="center" label="序号" class-name="index-column" />
                <el-table-column prop="employeeName" label="员工姓名" width="100" align="center" />
                <el-table-column prop="reportDate" label="日报日期" width="140" align="center" sortable>
                    <template #default="{ row }">
                        <el-tag
                            :type="isToday(row.reportDate) ? 'success' : 'info'"
                            effect="plain"
                        >
                            {{ formatDateWithYear(row.reportDate) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="客户统计" width="180" align="center">
                    <template #default="{ row }">
                        <div class="client-stats">
                            <div class="stat-row">
                                <span class="label">年度:</span>
                                <span class="value">{{ row.yearlyNewClients || 0 }}</span>
                                <span class="label">月度:</span>
                                <span class="value">{{ row.monthlyNewClients || 0 }}</span>
                            </div>
                            <div class="stat-row">
                                <span class="label">距上次:</span>
                                <span class="value">{{ row.daysSinceLastNewClient || 0 }}天</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="responsibilityLevel" label="责任心评级" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag 
                            :type="getResponsibilityTagType(row.responsibilityLevel)"
                            effect="light"
                        >
                            {{ row.responsibilityLevel }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="dailyResults" label="今日效果" min-width="200" show-overflow-tooltip align="center" />
                <el-table-column prop="managerEvaluation" label="评价" min-width="150" show-overflow-tooltip align="center">
                    <template #default="{ row }">
                        <span v-if="row.managerEvaluation" class="evaluation-text">
                            {{ row.managerEvaluation }}
                        </span>
                        <span v-else class="no-evaluation">暂无评价</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="提交时间" width="180" align="center" sortable>
                    <template #default="{ row }">
                        {{ formatDateTimeWithYear(row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" min-width="160" align="center">
                    <template #default="{ row }">
                        <div class="operation-buttons">
                            <el-button
                                type="primary"
                                size="small"
                                @click="viewReport(row)"
                                title="查看详情"
                            >
                                <el-icon><View /></el-icon>查看
                            </el-button>
                            <el-button
                                type="warning"
                                size="small"
                                @click="editReport(row)"
                                title="编辑日报"
                            >
                                <el-icon><Edit /></el-icon>编辑
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
        </div>

        <!-- 日报详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="日报详情"
            width="800px"
            destroy-on-close
            class="report-detail-dialog"
        >
            <div v-if="currentReport" class="report-detail">
                <!-- 员工信息 -->
                <div class="detail-section">
                    <h3>员工信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>员工姓名:</label>
                            <span>{{ currentReport.employeeName }}</span>
                        </div>
                        <div class="info-item">
                            <label>日报日期:</label>
                            <span>{{ formatDateWithYear(currentReport.reportDate) }}</span>
                        </div>
                        <div class="info-item">
                            <label>责任心评级:</label>
                            <el-tag :type="getResponsibilityTagType(currentReport.responsibilityLevel)">
                                {{ currentReport.responsibilityLevel }}
                            </el-tag>
                        </div>
                        <div class="info-item">
                            <label>提交时间:</label>
                            <span>{{ formatDateTimeWithYear(currentReport.createTime) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 客户统计 -->
                <div class="detail-section">
                    <h3>客户统计</h3>
                    <div class="stats-row">
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.yearlyNewClients || 0 }}</div>
                            <div class="stat-desc">年度新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.monthlyNewClients || 0 }}</div>
                            <div class="stat-desc">当月新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.daysSinceLastNewClient || 0 }}</div>
                            <div class="stat-desc">距上次新客户天数</div>
                        </div>
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="detail-section">
                    <h3>客户信息</h3>
                    <div class="client-info">
                        <div class="client-category">
                            <h4>询价客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'inquiry')"
                                    :key="client.id"
                                    type="info"
                                    class="client-tag"
                                >
                                    {{ client.name }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'inquiry').length" class="no-data">暂无</span>
                            </div>
                        </div>
                        <div class="client-category">
                            <h4>出货客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'shipping')"
                                    :key="client.id"
                                    type="success"
                                    class="client-tag"
                                >
                                    {{ client.name }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'shipping').length" class="no-data">暂无</span>
                            </div>
                        </div>
                        <div class="client-category">
                            <h4>重点开发客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'keyDevelopment')"
                                    :key="client.id"
                                    type="warning"
                                    class="client-tag"
                                >
                                    {{ client.name }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'keyDevelopment').length" class="no-data">暂无</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 下班准备 -->
                <div class="detail-section">
                    <h3>下班准备</h3>
                    <div class="checklist-display">
                        <div v-if="currentReport.endOfDayChecklist" class="checklist-items">
                            <div
                                v-for="(item, key) in getChecklistItems(currentReport.endOfDayChecklist)"
                                :key="key"
                                class="checklist-item"
                            >
                                <el-icon :class="item.completed ? 'completed' : 'incomplete'">
                                    <Check v-if="item.completed" />
                                    <Close v-else />
                                </el-icon>
                                <span>{{ item.label }}</span>
                            </div>
                        </div>
                        <span v-else class="no-data">暂无记录</span>
                    </div>
                </div>

                <!-- 工作记录 -->
                <div class="detail-section">
                    <h3>工作记录</h3>
                    <div class="work-records">
                        <div class="record-item">
                            <h4>今日效果</h4>
                            <div class="record-content">
                                {{ currentReport.dailyResults || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>会议报告</h4>
                            <div class="record-content">
                                {{ currentReport.meetingReport || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>工作日记</h4>
                            <div class="record-content">
                                {{ currentReport.workDiary || '暂无记录' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 领导评价 -->
                <div class="detail-section">
                    <h3>评价</h3>
                    <div class="evaluation-content">
                        <div v-if="currentReport.managerEvaluation" class="evaluation-text">
                            {{ currentReport.managerEvaluation }}
                        </div>
                        <div v-if="currentReport.evaluationTime" class="evaluation-info">
                            <span class="evaluation-time">评价时间：{{ formatDateTimeWithYear(currentReport.evaluationTime) }}</span>
                            <span v-if="currentReport.evaluatorName" class="evaluator-name">评价人：{{ currentReport.evaluatorName }}</span>
                        </div>
                        <div v-if="!currentReport.managerEvaluation" class="no-evaluation">
                            暂无评价
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 编辑日报对话框 -->
        <el-dialog
            v-model="editDialogVisible"
            title="编辑日报"
            width="900px"
            destroy-on-close
            class="edit-report-dialog"
        >
            <el-form
                v-if="editForm"
                ref="editFormRef"
                :model="editForm"
                :rules="editRules"
                label-width="120px"
                class="edit-form"
            >
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3>基本信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="员工姓名">
                                <el-input v-model="editForm.employeeName" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="日报日期">
                                <el-input v-model="editForm.reportDate" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 客户选择 -->
                <div class="form-section">
                    <h3>客户选择</h3>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="询价客户">
                                <el-select
                                    v-model="editForm.inquiryClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择询价客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出货客户">
                                <el-select
                                    v-model="editForm.shippingClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择出货客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="重点开发客户">
                                <el-select
                                    v-model="editForm.keyDevelopmentClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择重点开发客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 责任心评级 -->
                <div class="form-section">
                    <h3>责任心评级</h3>
                    <el-form-item label="责任心评级" prop="responsibilityLevel">
                        <el-radio-group v-model="editForm.responsibilityLevel">
                            <el-radio value="优秀">优秀</el-radio>
                            <el-radio value="中等">中等</el-radio>
                            <el-radio value="差">差</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>

                <!-- 下班准备 -->
                <div class="form-section">
                    <h3>下班准备</h3>
                    <el-form-item label="检查清单">
                        <el-checkbox-group v-model="editForm.endOfDayChecklistItems">
                            <el-checkbox value="整理完桌面">整理完桌面</el-checkbox>
                            <el-checkbox value="整理完50通预计电话邮件上级">整理完50通预计电话邮件上级</el-checkbox>
                            <el-checkbox value="会议已开完">会议已开完</el-checkbox>
                            <el-checkbox value="准备好明天工作资料">准备好明天工作资料</el-checkbox>
                            <el-checkbox value="问候领导后打卡离开">问候领导后打卡离开</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </div>

                <!-- 工作记录 -->
                <div class="form-section">
                    <h3>工作记录</h3>
                    <el-form-item label="今日效果" prop="dailyResults">
                        <el-input
                            v-model="editForm.dailyResults"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入今日工作效果"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item label="会议报告" prop="meetingReport">
                        <el-input
                            v-model="editForm.meetingReport"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入会议报告"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item label="工作日记" prop="workDiary">
                        <el-input
                            v-model="editForm.workDiary"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入工作日记"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                </div>

                <!-- 领导评价 -->
                <div class="form-section">
                    <h3>评价</h3>
                    <el-form-item label="评价内容">
                        <el-input
                            v-model="editForm.managerEvaluation"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入对该员工的评价"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                </div>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="editDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleEditSubmit" :loading="editLoading">
                        保存
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
    Search, RefreshRight, View, Edit, Check, Close
} from '@element-plus/icons-vue'
import {
    getDepartmentSalesReportPage,
    editDepartmentReport,
    getMyClients
} from '@/api/salesReport'
import { getResponsibleDepartmentTree } from '@/api/department'

// 响应式数据
const loading = ref(false)
const reportList = ref([])
const detailDialogVisible = ref(false)
const currentReport = ref(null)
const editDialogVisible = ref(false)
const editForm = ref(null)
const editFormRef = ref(null)
const editLoading = ref(false)

// 部门相关
const departmentOptions = ref([])
const loadingDepartments = ref(false)
const selectedDepartments = ref([])
const selectedDepartmentIds = ref([])
const leadingDepartmentIds = ref([]) // 用户负责的顶级部门ID
const cascaderProps = {
    value: 'value',
    label: 'label',
    children: 'children',
    checkStrictly: true, // 父子节点选择状态独立
    emitPath: false, // 只返回选中的节点的值，而不是整个路径
    expandTrigger: 'hover', // 鼠标悬停时展开子节点
    multiple: true // 启用多选模式
}

// 客户相关
const clientOptions = ref([])
const clientLoading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
    departmentIds: [],
    employeeName: '',
    dateRange: [],
    responsibilityLevel: ''
})

// 编辑表单验证规则
const editRules = {
    responsibilityLevel: [
        { required: true, message: '请选择责任心评级', trigger: 'change' }
    ],
    dailyResults: [
        { min: 1, max: 2000, message: '今日效果长度必须在1-2000字符之间', trigger: 'blur' }
    ],
    meetingReport: [
        { min: 1, max: 2000, message: '会议报告长度必须在1-2000字符之间', trigger: 'blur' }
    ],
    workDiary: [
        { min: 1, max: 2000, message: '工作日记长度必须在1-2000字符之间', trigger: 'blur' }
    ]
}

// 方法
const loadReportList = async () => {
    try {
        loading.value = true

        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }

        if (searchForm.employeeName) {
            params.employeeName = searchForm.employeeName
        }
        if (searchForm.dateRange && searchForm.dateRange.length === 2) {
            params.startDate = searchForm.dateRange[0]
            params.endDate = searchForm.dateRange[1]
        }
        if (searchForm.responsibilityLevel) {
            params.responsibilityLevel = searchForm.responsibilityLevel
        }
        if (searchForm.departmentIds && searchForm.departmentIds.length > 0) {
            // 将数组转换为逗号分隔的字符串，避免URL编码问题
            params.departmentIds = searchForm.departmentIds.join(',')
        }

        const response = await getDepartmentSalesReportPage(params)

        if (response.code === 200) {
            reportList.value = response.data.list || []
            total.value = response.data.total || 0
        }
    } catch (error) {
        ElMessage.error('获取部门日报列表失败')
        console.error('Load department report list error:', error)
    } finally {
        loading.value = false
    }
}

const getResponsibilityTagType = (level) => {
    switch (level) {
        case '优秀': return 'success'
        case '中等': return 'warning'
        case '差': return 'danger'
        default: return 'info'
    }
}



const isToday = (date) => {
    const today = new Date().toISOString().split('T')[0]
    return date === today
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
    })
}

const formatDateWithYear = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const formatDateTime = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatDateTimeWithYear = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const handleSearch = () => {
    currentPage.value = 1
    loadReportList()
}

const handleReset = () => {
    selectedDepartments.value = []
    selectedDepartmentIds.value = []
    searchForm.departmentIds = []
    searchForm.employeeName = ''
    searchForm.dateRange = []
    searchForm.responsibilityLevel = ''
    currentPage.value = 1
    loadReportList()
}

const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    loadReportList()
}

const handleCurrentChange = (page) => {
    currentPage.value = page
    loadReportList()
}

const viewReport = async (report) => {
    // 确保客户数据已加载
    if (clientOptions.value.length === 0) {
        await loadClientOptions()
    }

    currentReport.value = report
    detailDialogVisible.value = true
}

const editReport = async (report) => {
    // 先加载客户选项，确保客户数据可用
    await loadClientOptions()

    // 构建编辑表单数据
    editForm.value = {
        id: report.id,
        employeeId: report.employeeId,
        employeeName: report.employeeName,
        reportDate: report.reportDate, // 保持原始的YYYY-MM-DD格式
        inquiryClientIds: parseClientIds(report.inquiryClients),
        shippingClientIds: parseClientIds(report.shippingClients),
        keyDevelopmentClientIds: parseClientIds(report.keyDevelopmentClients),
        responsibilityLevel: report.responsibilityLevel,
        endOfDayChecklistItems: parseChecklistItems(report.endOfDayChecklist),
        dailyResults: report.dailyResults,
        meetingReport: report.meetingReport,
        workDiary: report.workDiary,
        managerEvaluation: report.managerEvaluation || ''
    }

    editDialogVisible.value = true
}

const handleEditSubmit = async () => {
    try {
        await editFormRef.value.validate()
        editLoading.value = true

        // 构建提交数据
        const submitData = {
            id: editForm.value.id,
            employeeId: editForm.value.employeeId,
            reportDate: editForm.value.reportDate,
            inquiryClientIds: editForm.value.inquiryClientIds,
            shippingClientIds: editForm.value.shippingClientIds,
            keyDevelopmentClientIds: editForm.value.keyDevelopmentClientIds,
            responsibilityLevel: editForm.value.responsibilityLevel,
            endOfDayChecklist: editForm.value.endOfDayChecklistItems, // 直接发送字符串数组
            dailyResults: editForm.value.dailyResults,
            meetingReport: editForm.value.meetingReport,
            workDiary: editForm.value.workDiary,
            managerEvaluation: editForm.value.managerEvaluation
        }

        const response = await editDepartmentReport(submitData)
        if (response.code === 200) {
            ElMessage.success('日报编辑成功')
            editDialogVisible.value = false
            loadReportList()
        } else {
            ElMessage.error(response.message || '编辑失败')
        }
    } catch (error) {
        console.error('Edit report error:', error)
        ElMessage.error('编辑失败')
    } finally {
        editLoading.value = false
    }
}

// 部门相关方法
const getAllChildDepartmentIds = (departmentId) => {
    const childIds = []
    const findDepartmentNode = (nodes, targetId) => {
        for (const node of nodes) {
            if (node.value === targetId) return node
            if (node.children && node.children.length) {
                const foundNode = findDepartmentNode(node.children, targetId)
                if (foundNode) return foundNode
            }
        }
        return null
    }
    const collectChildIds = (node) => {
        if (!node.children || !node.children.length) return
        for (const child of node.children) {
            childIds.push(child.value)
            collectChildIds(child)
        }
    }
    const departmentNode = findDepartmentNode(departmentOptions.value, departmentId)
    if (departmentNode) collectChildIds(departmentNode)
    return childIds
}

const convertTreeToOptions = (nodes) => {
    if (!nodes || !nodes.length) return []
    return nodes.map(node => ({
        value: node.departmentId,
        label: node.departmentName,
        children: convertTreeToOptions(node.children || [])
    }))
}

const loadDepartmentTree = async () => {
    try {
        loadingDepartments.value = true
        const response = await getResponsibleDepartmentTree()
        if (response.code === 200) {
            // 转换为级联选择器所需的格式
            departmentOptions.value = (response.data || []).map(dept => ({
                value: dept.departmentId,
                label: dept.departmentName,
                children: convertTreeToOptions(dept.children || [])
            }))

            // 记录用户负责的顶级部门ID
            leadingDepartmentIds.value = (response.data || []).map(dept => dept.departmentId)

            // 自动选择所有负责的部门和子部门
            if (leadingDepartmentIds.value.length > 0) {
                let allDepartmentIds = [...leadingDepartmentIds.value]
                for (const departmentId of leadingDepartmentIds.value) {
                    const childIds = getAllChildDepartmentIds(departmentId)
                    allDepartmentIds = allDepartmentIds.concat(childIds)
                }
                selectedDepartments.value = allDepartmentIds
                selectedDepartmentIds.value = allDepartmentIds
                searchForm.departmentIds = allDepartmentIds

                // 自动加载数据
                handleSearch()
            } else {
                ElMessage.warning('您没有任何可以查看日报的部门')
                // 清空列表，不发送请求
                reportList.value = []
                total.value = 0
            }
        }
    } catch (error) {
        console.error('Load department tree error:', error)
        ElMessage.error('加载部门信息失败')
    } finally {
        loadingDepartments.value = false
    }
}

const handleDepartmentChange = (values) => {
    if (!values || values.length === 0) {
        selectedDepartmentIds.value = []
        selectedDepartments.value = []
        searchForm.departmentIds = []
        handleSearch()
        return
    }

    const previousSelection = selectedDepartmentIds.value || []
    const newlySelected = values.filter(id => !previousSelection.includes(id))
    const deselected = previousSelection.filter(id => !values.includes(id))
    let allDepartmentIds = [...values]
    let uiSelectedDepartments = [...values]

    // 处理新选择的部门，自动选择其子部门
    for (const departmentId of newlySelected) {
        const childIds = getAllChildDepartmentIds(departmentId)
        if (childIds.length > 0) {
            childIds.forEach(childId => {
                if (!allDepartmentIds.includes(childId)) allDepartmentIds.push(childId)
                if (!uiSelectedDepartments.includes(childId)) uiSelectedDepartments.push(childId)
            })
        }
    }

    // 处理取消选择的部门，自动取消其子部门
    for (const departmentId of deselected) {
        const childIds = getAllChildDepartmentIds(departmentId)
        if (childIds.length > 0) {
            allDepartmentIds = allDepartmentIds.filter(id => !childIds.includes(id))
            uiSelectedDepartments = uiSelectedDepartments.filter(id => !childIds.includes(id))
        }
    }

    selectedDepartments.value = uiSelectedDepartments
    selectedDepartmentIds.value = allDepartmentIds
    searchForm.departmentIds = allDepartmentIds
    handleSearch()
}

// 客户相关方法
const loadClientOptions = async () => {
    try {
        const response = await getMyClients()
        if (response.code === 200) {
            clientOptions.value = response.data || []
        }
    } catch (error) {
        console.error('Load client options error:', error)
    }
}

const searchClients = async (query) => {
    if (query) {
        clientLoading.value = true
        try {
            const response = await getMyClients({ keyword: query })
            if (response.code === 200) {
                clientOptions.value = response.data || []
            }
        } catch (error) {
            console.error('Search clients error:', error)
        } finally {
            clientLoading.value = false
        }
    }
}

// 数据解析方法
const parseClientIds = (clientData) => {
    if (!clientData) return []
    try {
        const data = typeof clientData === 'string' ? JSON.parse(clientData) : clientData
        return data.clientIds || []
    } catch (error) {
        return []
    }
}

const parseChecklistItems = (checklistData) => {
    if (!checklistData) return []

    try {
        const data = typeof checklistData === 'string' ? JSON.parse(checklistData) : checklistData

        // 如果items是数组（当前格式），直接返回数组内容
        if (data.items && Array.isArray(data.items)) {
            return data.items
        }

        // 如果items是对象（标准格式），返回为true的键
        if (data.items && typeof data.items === 'object') {
            return Object.keys(data.items).filter(key => data.items[key])
        }

        return []
    } catch (error) {
        console.error('parseChecklistItems - 解析失败:', error)
        return []
    }
}

const buildChecklistData = (selectedItems) => {
    // 使用与SalesReportBasic.vue相同的格式
    return {
        items: selectedItems, // 直接使用选中的项目数组
        completedCount: selectedItems.length,
        totalCount: 5, // 总共5个检查项
        lastUpdated: new Date().toISOString()
    }
}

const getClientsByType = (report, type) => {
    const fieldMap = {
        inquiry: 'inquiryClients',
        shipping: 'shippingClients',
        keyDevelopment: 'keyDevelopmentClients'
    }

    const clientData = report[fieldMap[type]]
    const clientIds = parseClientIds(clientData)

    // 根据客户ID从客户选项中查找客户名称
    return clientIds.map(id => {
        const client = clientOptions.value.find(c => c.id === id)
        return {
            id,
            name: client ? client.name : `客户ID: ${id}`
        }
    })
}

const getChecklistItems = (checklistData) => {
    if (!checklistData) return []

    try {
        const data = typeof checklistData === 'string' ? JSON.parse(checklistData) : checklistData

        // 如果是数组格式（旧格式），直接返回
        if (Array.isArray(data)) {
            return data.map((item, index) => ({
                key: index,
                label: item,
                completed: true
            }))
        }

        // 如果是对象格式，检查items字段
        if (data.items) {
            // 如果items是数组（当前格式）
            if (Array.isArray(data.items)) {
                return data.items.map((item, index) => ({
                    key: index,
                    label: item,
                    completed: true
                }))
            }

            // 如果items是对象（标准格式）
            if (typeof data.items === 'object') {
                const labelMap = {
                    deskOrganized: '桌面整理',
                    emailsHandled: '邮件处理',
                    meetingsCompleted: '会议完成',
                    materialsReady: '资料准备',
                    greetedLeader: '向领导问好'
                }

                return Object.keys(data.items).map(key => ({
                    key,
                    label: labelMap[key] || key,
                    completed: data.items[key]
                }))
            }
        }

        return []
    } catch (error) {
        console.error('解析下班准备数据失败:', error)
        return []
    }
}

// 生命周期
onMounted(() => {
    loadDepartmentTree() // 这个函数会在加载完部门后自动调用handleSearch()加载数据
})
</script>

<style scoped>
.department-content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-section {
    flex: 0 0 auto;
}

.search-row {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.custom-table {
    margin-bottom: 60px; /* Space for pagination */
    border-radius: 6px;
    overflow: hidden; /* For rounded corners on table */
}

.index-column {
    background-color: #fafafa !important;
}

.client-stats {
    font-size: 12px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #909399;
}

.value {
    color: #303133;
    font-weight: 500;
}

.completion-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.completion-text {
    font-size: 12px;
    color: #606266;
}

.operation-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; /* Ensure it's above the table if overlapping */
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1; /* Ensure it's above other elements */
}

/* 详情对话框样式 */
.report-detail {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    text-align: center; /* 居中对齐 */
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item label {
    font-weight: 500;
    color: #606266;
    min-width: 80px;
}

.stats-row {
    display: flex;
    gap: 20px;
    justify-content: space-around;
}

.stat-box {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.stat-desc {
    font-size: 12px;
    color: #606266;
}

.work-records {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 14px;
    text-align: center; /* 居中对齐 */
}

.record-content {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
}

:deep(.el-table .cell) {
    padding: 8px 12px;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
}

/* 评价相关样式 */
.evaluation-text {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 10px;
}

.no-evaluation {
    color: #909399;
    font-style: italic;
}

.evaluation-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #909399;
    margin-top: 10px;
}

/* 客户信息样式 */
.client-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.client-category h4 {
    color: #606266;
    font-size: 14px;
    margin-bottom: 8px;
}

.client-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.client-tag {
    margin: 0;
}

.no-data {
    color: #909399;
    font-style: italic;
}

/* 检查清单样式 */
.checklist-display {
    padding: 10px;
}

.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checklist-item .completed {
    color: #67c23a;
}

.checklist-item .incomplete {
    color: #f56c6c;
}

/* 编辑对话框样式 */
.edit-report-dialog .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.edit-form {
    padding: 0;
}

.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #303133;
    font-size: 16px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    text-align: center; /* 居中对齐 */
}
</style>
