<template>
    <div class="department-content-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
                <div class="search-section">
                    <div class="search-row">
                        <el-input
                            v-model="searchForm.employeeName"
                            placeholder="搜索员工姓名"
                            clearable
                            style="width: 160px"
                            @keyup.enter="handleSearch"
                            @clear="handleSearch"
                        >
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-date-picker
                            v-model="searchForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 220px"
                            @change="handleSearch"
                        />
                        <el-select
                            v-model="searchForm.responsibilityLevel"
                            placeholder="责任心评级"
                            clearable
                            style="width: 120px"
                            @change="handleSearch"
                        >
                            <el-option label="优秀" value="优秀" />
                            <el-option label="中等" value="中等" />
                            <el-option label="差" value="差" />
                        </el-select>
                        <el-button type="primary" @click="handleSearch">
                            <el-icon><Search /></el-icon>搜索
                        </el-button>
                        <el-button @click="handleReset">
                            <el-icon><RefreshRight /></el-icon>重置
                        </el-button>
                    </div>
                </div>

                <div class="action-buttons">
                    <el-button
                        type="danger"
                        @click="handleBatchDelete"
                        :disabled="selectedReports.length === 0"
                    >
                        <el-icon><Delete /></el-icon>
                        批量删除
                    </el-button>
                </div>
            </div>

            <el-table
                v-loading="loading"
                :data="reportList"
                border
                row-key="id"
                :max-height="'calc(100vh - 220px)'"
                class="custom-table"
                :header-cell-style="{ background: '#f7f7f7', color: '#606266' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column type="index" width="60" align="center" label="序号" class-name="index-column" />
                <el-table-column prop="employeeName" label="员工姓名" width="100" align="center" />
                <el-table-column prop="departmentName" label="部门" width="120" align="center" />
                <el-table-column prop="reportDate" label="日报日期" width="140" align="center" sortable>
                    <template #default="{ row }">
                        <el-tag
                            :type="isToday(row.reportDate) ? 'success' : 'info'"
                            effect="plain"
                        >
                            {{ formatDateWithYear(row.reportDate) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="客户统计" width="180" align="center">
                    <template #default="{ row }">
                        <div class="client-stats">
                            <div class="stat-row">
                                <span class="label">年度:</span>
                                <span class="value">{{ row.yearlyNewClients || 0 }}</span>
                                <span class="label">月度:</span>
                                <span class="value">{{ row.monthlyNewClients || 0 }}</span>
                            </div>
                            <div class="stat-row">
                                <span class="label">距上次:</span>
                                <span class="value">{{ row.daysSinceLastNewClient || 0 }}天</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="responsibilityLevel" label="责任心评级" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag 
                            :type="getResponsibilityTagType(row.responsibilityLevel)"
                            effect="light"
                        >
                            {{ row.responsibilityLevel }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="dailyResults" label="今日效果" min-width="200" show-overflow-tooltip align="center" />
                <el-table-column prop="managerEvaluation" label="评价" min-width="150" show-overflow-tooltip align="center">
                    <template #default="{ row }">
                        <span v-if="row.managerEvaluation" class="evaluation-text">
                            {{ row.managerEvaluation }}
                        </span>
                        <span v-else class="no-evaluation">暂无评价</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="提交时间" width="180" align="center" sortable>
                    <template #default="{ row }">
                        {{ formatDateTimeWithYear(row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" min-width="220" align="center">
                    <template #default="{ row }">
                        <div class="operation-buttons">
                            <el-button
                                type="primary"
                                size="small"
                                @click="viewReport(row)"
                                title="查看详情"
                            >
                                <el-icon><View /></el-icon>查看
                            </el-button>
                            <el-button
                                type="warning"
                                size="small"
                                @click="editReport(row)"
                                title="编辑日报"
                            >
                                <el-icon><Edit /></el-icon>编辑
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click="deleteReport(row)"
                                title="删除日报"
                            >
                                <el-icon><Delete /></el-icon>删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <div class="pagination-container">
                <el-pagination
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    v-model:currentPage="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    :page-sizes="[10, 20, 50, 100]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
        </div>

        <!-- 日报详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="日报详情"
            width="800px"
            destroy-on-close
            class="report-detail-dialog"
        >
            <div v-if="currentReport" class="report-detail">
                <!-- 员工信息 -->
                <div class="detail-section">
                    <h3>员工信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>员工姓名:</label>
                            <span>{{ currentReport.employeeName }}</span>
                        </div>
                        <div class="info-item">
                            <label>日报日期:</label>
                            <span>{{ formatDateWithYear(currentReport.reportDate) }}</span>
                        </div>
                        <div class="info-item">
                            <label>责任心评级:</label>
                            <el-tag :type="getResponsibilityTagType(currentReport.responsibilityLevel)">
                                {{ currentReport.responsibilityLevel }}
                            </el-tag>
                        </div>
                        <div class="info-item">
                            <label>提交时间:</label>
                            <span>{{ formatDateTimeWithYear(currentReport.createTime) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 客户统计 -->
                <div class="detail-section">
                    <h3>客户统计</h3>
                    <div class="stats-row">
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.yearlyNewClients || 0 }}</div>
                            <div class="stat-desc">年度新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.monthlyNewClients || 0 }}</div>
                            <div class="stat-desc">当月新客户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">{{ currentReport.daysSinceLastNewClient || 0 }}</div>
                            <div class="stat-desc">距上次新客户天数</div>
                        </div>
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="detail-section">
                    <h3>客户信息</h3>
                    <div class="client-info">
                        <div class="client-category">
                            <h4>询价客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'inquiry')"
                                    :key="client.id"
                                    type="info"
                                    class="client-tag"
                                >
                                    {{ client.name || client.id }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'inquiry').length" class="no-data">暂无</span>
                            </div>
                        </div>
                        <div class="client-category">
                            <h4>出货客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'shipping')"
                                    :key="client.id"
                                    type="success"
                                    class="client-tag"
                                >
                                    {{ client.name || client.id }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'shipping').length" class="no-data">暂无</span>
                            </div>
                        </div>
                        <div class="client-category">
                            <h4>重点开发客户</h4>
                            <div class="client-list">
                                <el-tag
                                    v-for="client in getClientsByType(currentReport, 'keyDevelopment')"
                                    :key="client.id"
                                    type="warning"
                                    class="client-tag"
                                >
                                    {{ client.name || client.id }}
                                </el-tag>
                                <span v-if="!getClientsByType(currentReport, 'keyDevelopment').length" class="no-data">暂无</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 下班准备工作 -->
                <div class="detail-section" v-if="getParsedChecklistItems(currentReport).length > 0">
                    <h3>下班准备工作</h3>
                    <div class="checklist-display">
                        <div class="checklist-items">
                            <div v-for="item in getParsedChecklistItems(currentReport)" :key="item" class="checklist-item">
                                <el-icon class="check-icon"><Check /></el-icon>
                                <span>{{ item }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作记录 -->
                <div class="detail-section">
                    <h3>工作记录</h3>
                    <div class="work-records">
                        <div class="record-item">
                            <h4>今日效果</h4>
                            <div class="record-content">
                                {{ currentReport.dailyResults || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>会议报告</h4>
                            <div class="record-content">
                                {{ currentReport.meetingReport || '暂无记录' }}
                            </div>
                        </div>
                        <div class="record-item">
                            <h4>工作日记</h4>
                            <div class="record-content">
                                {{ currentReport.workDiary || '暂无记录' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 领导评价 -->
                <div class="detail-section">
                    <h3>领导评价</h3>
                    <div class="evaluation-content">
                        <div v-if="currentReport.managerEvaluation" class="evaluation-text">
                            {{ currentReport.managerEvaluation }}
                        </div>
                        <div v-if="currentReport.evaluationTime" class="evaluation-info">
                            <span class="evaluation-time">评价时间：{{ formatDateTimeWithYear(currentReport.evaluationTime) }}</span>
                            <span v-if="currentReport.evaluatorName" class="evaluator-name">评价人：{{ currentReport.evaluatorName }}</span>
                        </div>
                        <div v-if="!currentReport.managerEvaluation" class="no-evaluation">
                            暂无评价
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 编辑日报对话框 -->
        <el-dialog
            v-model="editDialogVisible"
            title="编辑日报"
            width="900px"
            destroy-on-close
            class="edit-report-dialog"
        >
            <el-form
                v-if="editForm"
                ref="editFormRef"
                :model="editForm"
                :rules="editRules"
                label-width="120px"
                class="edit-form"
            >
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3>基本信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="员工姓名">
                                <el-input v-model="editForm.employeeName" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="日报日期">
                                <el-input v-model="editForm.reportDate" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 客户选择 -->
                <div class="form-section">
                    <h3>客户选择</h3>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="询价客户">
                                <el-select
                                    v-model="editForm.inquiryClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择询价客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出货客户">
                                <el-select
                                    v-model="editForm.shippingClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择出货客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="重点开发客户">
                                <el-select
                                    v-model="editForm.keyDevelopmentClientIds"
                                    multiple
                                    filterable
                                    remote
                                    placeholder="选择重点开发客户"
                                    :remote-method="searchClients"
                                    :loading="clientLoading"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="client in clientOptions"
                                        :key="client.id"
                                        :label="client.name"
                                        :value="client.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 责任心评级 -->
                <div class="form-section">
                    <h3>责任心评级</h3>
                    <el-form-item label="责任心评级" prop="responsibilityLevel">
                        <el-radio-group v-model="editForm.responsibilityLevel">
                            <el-radio value="优秀">优秀</el-radio>
                            <el-radio value="中等">中等</el-radio>
                            <el-radio value="差">差</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>

                <!-- 下班准备 -->
                <div class="form-section">
                    <h3>下班准备</h3>
                    <el-form-item label="检查清单">
                        <el-checkbox-group v-model="editForm.endOfDayChecklistItems">
                            <el-checkbox value="整理完桌面">整理完桌面</el-checkbox>
                            <el-checkbox value="整理完50通预计电话邮件上级">整理完50通预计电话邮件上级</el-checkbox>
                            <el-checkbox value="会议已开完">会议已开完</el-checkbox>
                            <el-checkbox value="准备好明天工作资料">准备好明天工作资料</el-checkbox>
                            <el-checkbox value="问候领导后打卡离开">问候领导后打卡离开</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </div>

                <!-- 工作记录 -->
                <div class="form-section">
                    <h3>工作记录</h3>
                    <el-form-item label="今日效果" prop="dailyResults">
                        <el-input
                            v-model="editForm.dailyResults"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入今日工作效果"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item label="会议报告" prop="meetingReport">
                        <el-input
                            v-model="editForm.meetingReport"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入会议报告"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item label="工作日记" prop="workDiary">
                        <el-input
                            v-model="editForm.workDiary"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入工作日记"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                </div>

                <!-- 领导评价 -->
                <div class="form-section">
                    <h3>评价</h3>
                    <el-form-item label="评价内容">
                        <el-input
                            v-model="editForm.managerEvaluation"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入对该员工的评价"
                            maxlength="2000"
                            show-word-limit
                        />
                    </el-form-item>
                </div>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="editDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleEditSubmit" :loading="editLoading">
                        保存
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    Search, RefreshRight, View, Edit, Delete, Check
} from '@element-plus/icons-vue'
import {
    getAllSalesReportPage,
    deleteSalesReport,
    batchDeleteSalesReport,
    editDepartmentReport,
    getMyClients
} from '@/api/salesReport'

// 响应式数据
const loading = ref(false)
const reportList = ref([])
const detailDialogVisible = ref(false)
const currentReport = ref(null)
const selectedReports = ref([])
const editDialogVisible = ref(false)
const editForm = ref(null)
const editFormRef = ref(null)
const editLoading = ref(false)

// 客户相关
const clientOptions = ref([])
const clientLoading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
    employeeName: '',
    dateRange: [],
    responsibilityLevel: ''
})

// 编辑表单验证规则
const editRules = {
    responsibilityLevel: [
        { required: true, message: '请选择责任心评级', trigger: 'change' }
    ],
    dailyResults: [
        { min: 1, max: 2000, message: '今日效果长度必须在1-2000字符之间', trigger: 'blur' }
    ],
    meetingReport: [
        { min: 1, max: 2000, message: '会议报告长度必须在1-2000字符之间', trigger: 'blur' }
    ],
    workDiary: [
        { min: 1, max: 2000, message: '工作日记长度必须在1-2000字符之间', trigger: 'blur' }
    ]
}

// 方法
const loadReportList = async () => {
    try {
        loading.value = true

        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }

        if (searchForm.employeeName) {
            params.employeeName = searchForm.employeeName
        }
        if (searchForm.dateRange && searchForm.dateRange.length === 2) {
            params.startDate = searchForm.dateRange[0]
            params.endDate = searchForm.dateRange[1]
        }
        if (searchForm.responsibilityLevel) {
            params.responsibilityLevel = searchForm.responsibilityLevel
        }

        const response = await getAllSalesReportPage(params)

        if (response.code === 200) {
            reportList.value = response.data.list || []
            total.value = response.data.total || 0
        }
    } catch (error) {
        ElMessage.error('获取日报列表失败')
        console.error('Load report list error:', error)
    } finally {
        loading.value = false
    }
}

const getResponsibilityTagType = (level) => {
    switch (level) {
        case '优秀': return 'success'
        case '中等': return 'warning'
        case '差': return 'danger'
        default: return 'info'
    }
}

const getChecklistCompletion = (checklistData) => {
    if (!checklistData) return 0

    try {
        const checklist = typeof checklistData === 'string'
            ? JSON.parse(checklistData)
            : checklistData

        if (checklist.completedCount !== undefined && checklist.totalCount !== undefined) {
            return Math.round((checklist.completedCount / checklist.totalCount) * 100)
        }

        if (checklist.items) {
            const completed = Object.values(checklist.items).filter(Boolean).length
            const total = Object.keys(checklist.items).length
            return Math.round((completed / total) * 100)
        }
    } catch (error) {
        console.error('Parse checklist error:', error)
    }

    return 0
}

const isToday = (date) => {
    const today = new Date().toISOString().split('T')[0]
    return date === today
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
    })
}

const formatDateWithYear = (date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const formatDateTime = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatDateTimeWithYear = (datetime) => {
    return new Date(datetime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const handleSearch = () => {
    currentPage.value = 1
    loadReportList()
}

const handleReset = () => {
    searchForm.employeeName = ''
    searchForm.dateRange = []
    searchForm.responsibilityLevel = ''
    currentPage.value = 1
    loadReportList()
}

const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    loadReportList()
}

const handleCurrentChange = (page) => {
    currentPage.value = page
    loadReportList()
}

const handleSelectionChange = (selection) => {
    selectedReports.value = selection
}

const viewReport = async (report) => {
    // 确保客户数据已加载
    if (clientOptions.value.length === 0) {
        await loadClientOptions()
    }

    currentReport.value = report
    detailDialogVisible.value = true
}

const deleteReport = async (report) => {
    try {
        await ElMessageBox.confirm(
            `确定要删除 ${report.employeeName} 在 ${formatDate(report.reportDate)} 的日报吗？`,
            '删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        const response = await deleteSalesReport(report.id)
        if (response.code === 200) {
            ElMessage.success('删除成功')
            loadReportList()
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败')
            console.error('Delete report error:', error)
        }
    }
}

const handleBatchDelete = async () => {
    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedReports.value.length} 条日报吗？`,
            '批量删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        const ids = selectedReports.value.map(report => report.id)
        const response = await batchDeleteSalesReport(ids)
        if (response.code === 200) {
            ElMessage.success('批量删除成功')
            selectedReports.value = []
            loadReportList()
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('批量删除失败')
            console.error('Batch delete error:', error)
        }
    }
}

// 加载客户选项
const loadClientOptions = async () => {
    try {
        clientLoading.value = true
        const response = await getMyClients({ limit: 100 })
        if (response.code === 200) {
            clientOptions.value = response.data || []
        }
    } catch (error) {
        ElMessage.error('获取客户列表失败')
        console.error('Load client options error:', error)
    } finally {
        clientLoading.value = false
    }
}

// 远程搜索客户
const searchClients = async (keyword) => {
    if (!keyword) {
        await loadClientOptions()
        return
    }

    try {
        clientLoading.value = true
        const response = await getMyClients({ keyword, limit: 10 })
        if (response.code === 200) {
            clientOptions.value = response.data || []
        }
    } catch (error) {
        console.error('Search clients error:', error)
    } finally {
        clientLoading.value = false
    }
}

// 编辑日报
const editReport = async (report) => {
    // 先加载客户选项，确保客户数据可用
    await loadClientOptions()

    // 构建编辑表单数据
    editForm.value = {
        id: report.id,
        employeeId: report.employeeId,
        employeeName: report.employeeName,
        reportDate: report.reportDate,
        inquiryClientIds: parseClientIds(report.inquiryClients),
        shippingClientIds: parseClientIds(report.shippingClients),
        keyDevelopmentClientIds: parseClientIds(report.keyDevelopmentClients),
        responsibilityLevel: report.responsibilityLevel,
        endOfDayChecklistItems: parseChecklistItems(report.endOfDayChecklist),
        dailyResults: report.dailyResults,
        meetingReport: report.meetingReport,
        workDiary: report.workDiary,
        managerEvaluation: report.managerEvaluation || ''
    }

    editDialogVisible.value = true
}

// 处理编辑提交
const handleEditSubmit = async () => {
    try {
        await editFormRef.value.validate()
        editLoading.value = true

        // 构建提交数据
        const submitData = {
            id: editForm.value.id,
            employeeId: editForm.value.employeeId,
            reportDate: editForm.value.reportDate,
            inquiryClientIds: editForm.value.inquiryClientIds,
            shippingClientIds: editForm.value.shippingClientIds,
            keyDevelopmentClientIds: editForm.value.keyDevelopmentClientIds,
            responsibilityLevel: editForm.value.responsibilityLevel,
            endOfDayChecklist: editForm.value.endOfDayChecklistItems,
            dailyResults: editForm.value.dailyResults,
            meetingReport: editForm.value.meetingReport,
            workDiary: editForm.value.workDiary,
            managerEvaluation: editForm.value.managerEvaluation
        }

        const response = await editDepartmentReport(submitData)
        if (response.code === 200) {
            ElMessage.success('日报编辑成功')
            editDialogVisible.value = false
            loadReportList()
        } else {
            ElMessage.error(response.message || '编辑失败')
        }
    } catch (error) {
        ElMessage.error('编辑失败')
        console.error('Edit report error:', error)
    } finally {
        editLoading.value = false
    }
}

// 解析客户ID
const parseClientIds = (clientData) => {
    try {
        if (!clientData) return []
        const parsed = typeof clientData === 'string' ? JSON.parse(clientData) : clientData
        return parsed.clientIds || []
    } catch (error) {
        console.error('解析客户数据失败:', error)
        return []
    }
}

// 解析检查清单项目
const parseChecklistItems = (checklistData) => {
    try {
        if (!checklistData) return []
        const parsed = typeof checklistData === 'string' ? JSON.parse(checklistData) : checklistData
        return parsed.items || []
    } catch (error) {
        console.error('解析检查清单数据失败:', error)
        return []
    }
}

// 获取客户信息按类型（优先显示客户名称，没有名称则显示ID）
const getClientsByType = (report, type) => {
    if (!report) return []

    let clientIds = []
    try {
        switch (type) {
            case 'inquiry':
                clientIds = parseClientIds(report.inquiryClients)
                break
            case 'shipping':
                clientIds = parseClientIds(report.shippingClients)
                break
            case 'keyDevelopment':
                clientIds = parseClientIds(report.keyDevelopmentClients)
                break
        }

        return clientIds.map(id => {
            // 尝试从客户选项中找到客户信息
            const clientInfo = clientOptions.value.find(client => client.id === id)
            if (clientInfo) {
                return {
                    id: id,
                    name: clientInfo.name
                }
            } else {
                // 如果没有找到客户信息，只返回ID
                return {
                    id: id,
                    name: null
                }
            }
        })
    } catch (error) {
        console.error('获取客户信息失败:', error)
        return []
    }
}

// 解析下班准备工作JSON字符串
const parseChecklistData = (checklistJsonString) => {
    try {
        if (!checklistJsonString) return []
        const parsed = JSON.parse(checklistJsonString)
        return parsed.items || []
    } catch (error) {
        console.error('解析检查清单数据失败:', error)
        return []
    }
}

// 获取解析后的检查清单项目
const getParsedChecklistItems = (report) => {
    if (!report) return []
    return parseChecklistData(report.endOfDayChecklist)
}

// 获取客户ID按类型（直接显示ID，与部门日报保持一致）
const getClientIdsByType = (report, type) => {
    if (!report) return []

    try {
        switch (type) {
            case 'inquiry':
                return parseClientIds(report.inquiryClients)
            case 'shipping':
                return parseClientIds(report.shippingClients)
            case 'keyDevelopment':
                return parseClientIds(report.keyDevelopmentClients)
            default:
                return []
        }
    } catch (error) {
        console.error('获取客户ID失败:', error)
        return []
    }
}

// 生命周期
onMounted(() => {
    loadReportList()
})
</script>

<style scoped>
.department-content-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-section {
    flex: 0 0 auto;
}

.search-row {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.custom-table {
    border-radius: 8px;
    overflow: hidden;
}

.index-column {
    background-color: #fafafa !important;
}

.client-stats {
    font-size: 12px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #909399;
}

.value {
    color: #303133;
    font-weight: 500;
}

.completion-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.completion-text {
    font-size: 12px;
    color: #606266;
}

.operation-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; /* Ensure it's above the table if overlapping */
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1; /* Ensure it's above other elements */
}

/* 详情对话框样式 */
.report-detail {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item label {
    font-weight: 500;
    color: #606266;
    min-width: 80px;
}

.stats-row {
    display: flex;
    gap: 20px;
    justify-content: space-around;
}

.stat-box {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.stat-desc {
    font-size: 12px;
    color: #606266;
}

.work-records {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 14px;
}

.record-content {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
}

:deep(.custom-table) {
    margin-bottom: 60px; /* Space for pagination */
    border-radius: 6px;
    overflow: hidden; /* For rounded corners on table */
}

:deep(.el-table .cell) {
    padding: 8px 12px;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
}

/* 客户信息样式 */
.client-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.client-category h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
}

.client-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.client-tag {
    margin: 0;
}

.no-data {
    color: #909399;
    font-style: italic;
}

/* 检查清单样式 */
.checklist-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border-radius: 6px;
    border-left: 3px solid #67c23a;
}

.check-icon {
    color: #67c23a;
    font-size: 16px;
}

/* 评价相关样式 */
.evaluation-text {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 10px;
}

.no-evaluation {
    color: #909399;
    font-style: italic;
}

.evaluation-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
}

.evaluation-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #909399;
    margin-top: 10px;
}

.evaluation-time,
.evaluator-name {
    display: flex;
    align-items: center;
}

/* 编辑对话框样式 */
.edit-report-dialog {
    max-height: 90vh;
}

.edit-form {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
}

.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
