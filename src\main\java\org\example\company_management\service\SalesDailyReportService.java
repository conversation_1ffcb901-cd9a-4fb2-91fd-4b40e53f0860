package org.example.company_management.service;

import org.example.company_management.entity.SalesDailyReport;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.dto.ClientStatisticsDto;
import org.example.company_management.dto.DepartmentReportEditDTO;
import org.example.company_management.dto.SalesDailyReportDto;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * {{CHENGQI: 销售日报服务接口}}
 * {{CHENGQI: 任务ID: P2-LD-005}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报核心业务逻辑接口，遵循接口隔离原则}}
 */
public interface SalesDailyReportService {
    
    // ==================== 核心业务操作 ====================
    
    /**
     * 提交或更新销售日报
     * @param report 销售日报对象
     * @return 操作结果
     */
    Result<SalesDailyReport> submitReport(SalesDailyReport report);
    
    /**
     * 根据ID获取销售日报详情
     * @param id 日报ID
     * @return 日报详情
     */
    Result<SalesDailyReport> getReportById(Long id);
    
    /**
     * 获取员工指定日期的日报
     * @param employeeId 员工ID
     * @param reportDate 日报日期
     * @return 日报详情
     */
    Result<SalesDailyReport> getReportByEmployeeAndDate(Integer employeeId, LocalDate reportDate);
    
    /**
     * 删除销售日报
     * @param id 日报ID
     * @param currentEmployeeId 当前操作员工ID
     * @return 操作结果
     */
    Result<Void> deleteReport(Long id, Integer currentEmployeeId);

    /**
     * 批量删除销售日报（管理员权限）
     * @param ids 日报ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteReports(List<Long> ids);

    // ==================== 查询操作 ====================
    
    /**
     * 分页查询销售日报列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<SalesDailyReport> getReportPage(Integer pageNum, Integer pageSize, Map<String, Object> params);
    
    /**
     * 获取员工的日报列表
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日报列表
     */
    Result<List<SalesDailyReport>> getReportsByEmployee(Integer employeeId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取部门的日报列表（部门负责人权限）
     * @param departmentId 部门ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日报列表
     */
    Result<List<SalesDailyReport>> getReportsByDepartment(Integer departmentId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取员工最近的日报
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return 最近日报列表
     */
    Result<List<SalesDailyReport>> getRecentReports(Integer employeeId, Integer limit);

    /**
     * 获取员工负责的客户列表（支持搜索，限制最多10个）
     * @param employeeId 员工ID
     * @param name 客户名称（可选）
     * @param category 客户分类（可选）
     * @param status 客户状态（可选）
     * @return 客户列表
     */
    Result<List<Map<String, Object>>> getMyClients(Integer employeeId, String name, String category, String status);

    /**
     * 获取员工负责的客户列表（支持搜索，自定义限制数量）
     * @param employeeId 员工ID
     * @param name 客户名称（可选）
     * @param category 客户分类（可选）
     * @param status 客户状态（可选）
     * @param limit 限制数量
     * @return 客户列表
     */
    Result<List<Map<String, Object>>> getMyClients(Integer employeeId, String name, String category, String status, Integer limit);

    /**
     * 获取员工客户统计信息
     * @param employeeId 员工ID
     * @return 客户统计信息
     */
    Result<ClientStatisticsDto> getMyClientStatistics(Integer employeeId);
    
    // ==================== 统计分析（已删除冗余接口） ====================
    // 注：删除了未使用的统计方法：getReportStatistics, getResponsibilityDistribution, getChecklistCompletionStats
    
    // ==================== 业务验证 ====================
    
    /**
     * 检查员工是否已提交指定日期的日报
     * @param employeeId 员工ID
     * @param reportDate 日报日期
     * @return 是否已提交
     */
    boolean hasSubmittedReport(Integer employeeId, LocalDate reportDate);
    
    /**
     * 验证员工是否有权限访问指定日报
     * @param reportId 日报ID
     * @param currentEmployeeId 当前员工ID
     * @param currentEmployeeRole 当前员工角色
     * @param currentDepartmentId 当前员工部门ID
     * @return 是否有权限
     */
    boolean hasPermissionToAccess(Long reportId, Integer currentEmployeeId, String currentEmployeeRole, Integer currentDepartmentId);
    
    /**
     * 验证员工是否有权限编辑指定日报
     * @param reportId 日报ID
     * @param currentEmployeeId 当前员工ID
     * @param currentEmployeeRole 当前员工角色
     * @return 是否有权限编辑
     */
    boolean hasPermissionToEdit(Long reportId, Integer currentEmployeeId, String currentEmployeeRole);

    /**
     * 部门负责人编辑员工日报
     * @param dto 编辑DTO
     * @param currentEmployeeId 当前员工ID（评价人）
     * @return 操作结果
     */
    Result<String> editDepartmentReport(DepartmentReportEditDTO dto, Integer currentEmployeeId);

    /**
     * 更新日报评价
     * @param reportId 日报ID
     * @param managerEvaluation 领导评价
     * @param evaluatorId 评价人ID
     * @return 操作结果
     */
    Result<String> updateEvaluation(Long reportId, String managerEvaluation, Integer evaluatorId);
}

// {{CHENGQI: Service接口定义完成，下一步创建实现类}}
